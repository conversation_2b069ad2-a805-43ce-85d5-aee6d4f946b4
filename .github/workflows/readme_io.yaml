name: Sync Readme OpenAPI
on:
  workflow_dispatch:
  push:
    branches:
      - develop
      - staging
      - master
jobs:
  dump_specs:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v3
        with:
          python-version: 3.13
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install fastapi
          # Add any other dependencies your FastAPI apps might need
      - name: Dump specs
        run: |
          python dump_openapi.py file_service
          python dump_openapi.py auth_service
          # Add any other FastAPI services you have
      - name: Upload specs
        uses: actions/upload-artifact@v4
        with:
          name: openapi-specs
          path: |
            file_service_openapi.json
            auth_service_openapi.json
            # Add any other FastAPI services you have
      - name: Sync to ReadMe
        uses: readmeio/github-readme-sync@v1
        with:
          readme-oas-key: ${{ secrets.README_OAS_KEY }}
          oas-file-path: file_service_openapi.json
          version: 1.0.0
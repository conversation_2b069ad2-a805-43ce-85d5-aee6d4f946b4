name: Sync ReadMe OpenAPI

on:
  push:
    branches:
      - develop
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  BUILD_VERSION: ${{ github.sha }}
  DOCKER_NETWORK: stack

jobs:
  sync_openapi:
    runs-on: [self-hosted, prv, worker]
    env:
      DOCKER_BUILDKIT: 1
      COMPOSE_DOCKER_CLI_BUILD: 1

    steps:
      - name: Create subdirectory
        run: |
          rm -rf ${{ github.sha }}
          mkdir ${{ github.sha }}

      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}
          path: ${{ github.sha }}

      - name: Copy testing environment setup
        run: |
          cp -f settings/.env.local.sample settings/.env.local
          echo >> settings/.env.local
          cp -f infrastructure/testing/Makefile .
          cp -f infrastructure/testing/docker-compose.yaml .

          # Add secrets/env vars if required (adjust as needed)
          echo AMBEE_APIKEY=${{ secrets.AMBEE_APIKEY }} >> settings/.env.local
          echo WAPI_KEY=${{ secrets.WAPI_KEY }} >> settings/.env.local
          echo YOUTUBE_API_KEY=${{ secrets.YOUTUBE_API_KEY }} >> settings/.env.local
          echo COST_NOTIFICATION_BOT_TOKEN=${{ secrets.COST_NOTIFICATION_BOT_TOKEN }} >> settings/.env.local
          echo PIPELINE_RUN=true >> settings/.env.local
          echo OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }} >> settings/.env.local
          echo GROQ_API_KEY=${{ secrets.GROQ_API_KEY }} >> settings/.env.local
          echo VISUALCROSSING_APIKEY=${{ secrets.VISUALCROSSING_APIKEY }} >> settings/.env.local
        working-directory: ${{ github.sha }}

      - name: Build stack in docker
        run: make rebuild
        working-directory: ${{ github.sha }}

      - name: Bring up stack in docker
        run: make launch
        working-directory: ${{ github.sha }}

      - name: Provision infrastructure
        run: make provision_infrastructure
        working-directory: ${{ github.sha }}

      - name: Wait for the cluster
        run: make await_infrastructure
        working-directory: ${{ github.sha }}

      - name: Reinitialize opensearch indices, load samples
        run: |
          make reinitialize_indices
          make load_samples
        working-directory: ${{ github.sha }}

      - name: Generate OpenAPI stable JSON
        run: make export_openapi  # <-- Make sure this generates openapi-stable.json
        working-directory: ${{ github.sha }}

      - name: Install rdme CLI
        run: npm install -g rdme

      - name: Sync OpenAPI spec to ReadMe
        env:
          README_API_KEY: ${{ secrets.README_API_KEY }}
        run: |
          rdme openapi openapi-stable.json --key=$README_API_KEY --id=<YOUR_README_DEFINITION_ID>
        working-directory: ${{ github.sha }}

      - name: Cleanup
        if: always()
        run: |
          docker compose -p ${{ env.BUILD_VERSION }} down --remove-orphans || true
          docker network prune -f || true
          docker container prune -f || true
          docker volume prune -f || true
          rm -rf ${{ github.sha }}

